"""
Bot de trading automatizado para OKX Exchange utilizando estratégia Swing com Trailing Stop.
"""

import warnings
warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import asyncio
import numpy as np
import pandas as pd
import uuid
import time
from dotenv import load_dotenv
from typing import Dict, List, Optional, Tuple, Any

# Importações dos módulos compartilhados
from core.config import BotConfig, OrderCache
from core.base_bot import TradingBotBase, initialize_bot_components, send_startup_notification
from core.okx_client import OKXClient
from indicators.indicators import TechnicalIndicator
from utils.logger import TradingLogger
from utils.check_orders import check_trailing_stop_orders
from utils.order_validator import OrderValidator
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from signals.swing import SwingSignalGenerator

load_dotenv()


class OKXOrder:
    """Classe para gerenciar operações de ordens na exchange OKX com estratégia Trailing Stop para Swing."""

    _instance = None

    def __new__(cls, client: OKXClient = None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, client: OKXClient = None):
        if getattr(self, "_initialized", False):
            return
        self._initialized = True
        self.client = client or OKXClient()
        self.logger = TradingLogger.get_logger(__name__)
        self.validator = OrderValidator(self.client)
        # Garantir que o cache de ordens esteja inicializado no cliente
        if not hasattr(self.client, "order_cache"):
            self.client.order_cache = OrderCache(cache_duration=30)

    def validate_order_conditions(self, symbol: str) -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.

        Args:
            symbol: Par de trading

        Returns:
            True se condições estão OK, False caso contrário
        """
        return self.validator.validate_order_conditions(symbol, strategy_type="trailing")

    def _generate_client_order_id(self) -> str:
        return uuid.uuid4().hex[:32]

    def has_active_trailing_stop(self, symbol: str) -> bool:
        """
        Verifica especificamente se há ordens trailing stop ativas.

        Args:
            symbol: Par de trading

        Returns:
            True se há ordens trailing stop ativas, False caso contrário
        """
        try:
            # Verificar primeiro no cache
            open_orders = self.client.order_cache.get_open_orders(symbol)
            if open_orders is not None:
                trailing_orders = [
                    order
                    for order in open_orders
                    if "move_order_stop" in order.get("info", {}).get("ordType", "")
                ]
                active_count = len(trailing_orders)
                if active_count > 0:
                    return True
                return False

            # Se não estiver no cache, buscar da exchange
            trailing_orders = check_trailing_stop_orders(self.client, symbol)
            active_count = len(trailing_orders)
            if active_count > 0:
                for order in trailing_orders:
                    order_id = order.get("id", "N/A")
                    status = order.get("status", "N/A")
                    self.logger.debug(
                        "Trailing stop ativo: ID=%s, Status=%s", order_id, status
                    )
                return True
            return False
        except Exception as exc:
            self.logger.debug("Erro ao buscar dados para %s: %s", symbol, str(exc))
            self.logger.info("O símbolo %s não existe na exchange.", symbol)
            return True  # Em caso de erro, assume que há ordens ativas para ser cauteloso

    async def wait_for_order_execution(
        self, order_id: str, symbol: str, timeout: int = 60, max_retries: int = 3
    ) -> Optional[Dict]:
        start_time = time.time()
        retries = 0
        while time.time() - start_time < timeout:
            try:
                order = self.client.exchange.fetch_order(order_id, symbol)
                if order["status"] == "closed":
                    self.logger.info(f"Ordem {order_id} executada com sucesso.")
                    return order
                elif order["status"] == "canceled":
                    self.logger.warning(f"Ordem {order_id} foi cancelada.")
                    return None
                await asyncio.sleep(2)  # Aumentar o intervalo entre tentativas
            except Exception as exc:
                retries += 1
                self.logger.error(
                    f"Erro ao verificar status da ordem {order_id} (Tentativa {retries}/{max_retries}): {exc}"
                )
                if retries >= max_retries:
                    self.logger.error(
                        f"Limite de tentativas atingido para a ordem {order_id}. Abortando."
                    )
                    return None
                await asyncio.sleep(3)  # Aguardar mais tempo antes de nova tentativa
        self.logger.warning(
            f"Tempo esgotado ao esperar pela execução da ordem {order_id}."
        )
        return None

    async def place_buy_order_with_trailing_stop(
        self, symbol: str, indicator: TechnicalIndicator, timeframe: str = "15m"
    ) -> Optional[Dict]:
        try:
            if check_trailing_stop_orders(self.client, symbol):
                self.logger.info(
                    "Já existem ordens abertas para %s, pulando criação", symbol
                )
                return None

            best_bid = self.client.get_best_bid(symbol)
            if not best_bid:
                self.logger.error("Não foi possível obter melhor bid para %s", symbol)
                return None
            entry_price = best_bid
            self.logger.debug("Melhor bid obtido para %s: %s", symbol, entry_price)
            balance = self.client.get_balance()
            quote_currency = symbol.split("/")[1]
            available_balance = balance["total"].get(quote_currency, 0)
            if available_balance <= 0:
                self.logger.error(
                    "Saldo insuficiente em %s para %s", quote_currency, symbol
                )
                return None
            # Ajuste dinâmico do tamanho da posição baseado em volatilidade
            position_size = 0.1  # Valor padrão
            if (
                hasattr(self.client, "config")
                and hasattr(self.client.config, "ENABLE_DYNAMIC_POSITION_SIZING")
                and self.client.config.ENABLE_DYNAMIC_POSITION_SIZING
            ):
                atr = indicator.calculate_atr(symbol, timeframe, period=14)
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol,
                        timeframe,
                        limit=getattr(self.client.config, "ATR_LOOKBACK_PERIODS", 20)
                        + 14,
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0
                        self.logger.info(
                            "Volatilidade para %s: ATR atual=%.2f, ATR médio=%.2f, Razão=%.2f",
                            symbol,
                            atr,
                            atr_mean,
                            volatility_ratio,
                        )

                        if volatility_ratio > getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        ):
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MIN", 0.05
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, reduzindo tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        elif volatility_ratio < getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        ):
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_MAX", 0.15
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, aumentando tamanho da posição para %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                        else:
                            position_size = getattr(
                                self.client.config, "POSITION_SIZE_BASE", 0.1
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, tamanho da posição padrão %.1f%%",
                                symbol,
                                position_size * 100,
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando tamanho padrão",
                            symbol,
                        )
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando tamanho padrão",
                        symbol,
                    )
            else:
                position_size = 0.1  # Padrão fixo se desativado

            balance_to_use = available_balance * position_size
            amount = balance_to_use / entry_price
            formatted_amount = self.client.format_amount_with_precision(symbol, amount)
            formatted_price = self.client.format_price_with_precision(
                symbol, entry_price
            )
            if float(formatted_amount) <= 0:
                self.logger.error(
                    "Quantidade calculada insuficiente para %s: %s",
                    symbol,
                    formatted_amount,
                )
                return None
            self.logger.debug(
                "Calculado para %s - Quantidade: %s, Preço: %s",
                symbol,
                formatted_amount,
                formatted_price,
            )

            limit_order = self.client.exchange.create_order(
                symbol=symbol,
                type="limit",
                side="buy",
                amount=float(formatted_amount),
                price=float(formatted_price),
                params={"tdMode": "cash", "clOrdId": self._generate_client_order_id()},
            )
            if "amount" not in limit_order or limit_order["amount"] is None:
                limit_order["amount"] = float(formatted_amount)
            self.logger.info(
                "Ordem limite criada com sucesso: %s", limit_order.get("id")
            )

            # Aguardar execução da ordem limite
            executed_order = await self.wait_for_order_execution(
                limit_order["id"], symbol
            )
            if not executed_order:
                self.logger.error(
                    f"Ordem de compra {limit_order['id']} não foi executada."
                )
                return None

            entry_price_real = (
                executed_order["average"]
                if "average" in executed_order
                else executed_order["price"]
            )

            # Calcular callback ratio com 1.75 * ATR
            atr = indicator.calculate_atr(symbol, timeframe, period=14)
            if atr is None:
                self.logger.error("Não foi possível calcular ATR para %s", symbol)
                return None

            # Ajuste dinâmico do callback_ratio baseado em volatilidade
            callback_ratio_base = (1.75 * atr) / entry_price_real
            if (
                hasattr(self.client, "config")
                and hasattr(self.client.config, "ENABLE_DYNAMIC_TRAILING_RATIO")
                and self.client.config.ENABLE_DYNAMIC_TRAILING_RATIO
            ):
                if atr is not None:
                    # Calcular média histórica de ATR para comparação
                    ohlcv_data = indicator.fetch_historical_data(
                        symbol,
                        timeframe,
                        limit=getattr(self.client.config, "ATR_LOOKBACK_PERIODS", 20)
                        + 14,
                    )
                    if ohlcv_data and len(ohlcv_data) > 14:
                        atr_values = []
                        for i in range(len(ohlcv_data) - 14, len(ohlcv_data)):
                            high = np.array(
                                [candle[2] for candle in ohlcv_data[i - 14 : i]]
                            )
                            low = np.array(
                                [candle[3] for candle in ohlcv_data[i - 14 : i]]
                            )
                            close = np.array(
                                [candle[4] for candle in ohlcv_data[i - 14 : i]]
                            )
                            atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                            atr_values.append(atr_val)
                        atr_mean = np.mean(atr_values) if atr_values else atr
                        volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0

                        if volatility_ratio > getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_HIGH", 1.2
                        ):
                            callback_ratio = max(
                                getattr(self.client.config, "CALLBACK_RATIO_MAX", 0.08),
                                callback_ratio_base,
                            )
                            self.logger.info(
                                "Alta volatilidade detectada para %s, aumentando callback ratio para %.2f%%",
                                symbol,
                                callback_ratio * 100,
                            )
                        elif volatility_ratio < getattr(
                            self.client.config, "VOLATILITY_THRESHOLD_LOW", 0.8
                        ):
                            callback_ratio = min(
                                getattr(self.client.config, "CALLBACK_RATIO_MIN", 0.015),
                                callback_ratio_base,
                            )
                            self.logger.info(
                                "Baixa volatilidade detectada para %s, reduzindo callback ratio para %.2f%%",
                                symbol,
                                callback_ratio * 100,
                            )
                        else:
                            callback_ratio = max(
                                getattr(self.client.config, "CALLBACK_RATIO_BASE", 0.03),
                                min(
                                    callback_ratio_base,
                                    getattr(self.client.config, "CALLBACK_RATIO_MAX", 0.08),
                                ),
                            )
                            self.logger.info(
                                "Volatilidade normal para %s, callback ratio padrão %.2f%%",
                                symbol,
                                callback_ratio * 100,
                            )
                    else:
                        self.logger.warning(
                            "Dados históricos insuficientes para calcular média ATR de %s, usando callback ratio base",
                            symbol,
                        )
                        callback_ratio = max(0.015, min(0.08, callback_ratio_base))
                else:
                    self.logger.warning(
                        "Não foi possível calcular ATR para %s, usando limites padrão para callback ratio",
                        symbol,
                    )
                    callback_ratio = max(0.015, min(0.08, callback_ratio_base))
            else:
                callback_ratio = max(0.015, min(0.08, callback_ratio_base))  # Padrão fixo se desativado

            trailing_params = {
                "instId": self.client.exchange.markets[symbol]["id"],
                "tdMode": "cash",
                "side": "sell",
                "ordType": "move_order_stop",
                "sz": formatted_amount,
                "callbackRatio": str(callback_ratio),
                "activePx": "",
                "clOrdId": self._generate_client_order_id(),
            }

            trailing_order = self.client.exchange.private_post_trade_order_algo(
                trailing_params
            )

            if trailing_order and trailing_order.get("code") == "0":
                algo_id = trailing_order.get("data", [{}])[0].get("algoId", "N/A")
                self.logger.info("Trailing stop criado com sucesso: %s", algo_id)
                # Salvar o trailing stop no banco de dados
                trailing_stop_data = {
                    "algo_id": algo_id,
                    "order_id": limit_order.get("id"),
                    "symbol": symbol,
                    "callback_ratio": callback_ratio,
                    "status": "active",
                }

                bot = TradingBot()  # Instância do bot para acessar o banco de dados
                bot.db.save_trailing_stop(trailing_stop_data)
            else:
                self.logger.error("Falha ao criar trailing stop: %s", trailing_order)

            result = {
                "limit_order": limit_order,
                "entry_price": entry_price_real,
                "amount": float(formatted_amount),
                "symbol": symbol,
            }
            self.logger.info("Ordem com Trailing Stop processada com sucesso para %s", symbol)
            return result

        except Exception as exc:
            self.logger.error("Erro ao colocar ordem para %s: %s", symbol, str(exc))
            return None

    def log_order_prevention_details(self, symbol: str) -> None:
        try:
            trailing_orders = check_trailing_stop_orders(self.client, symbol)
            if trailing_orders:
                self.logger.info("=== PREVENÇÃO DE ORDEM ATIVADA ===")
                self.logger.info("Símbolo: %s", symbol)
                self.logger.info("Trailing stops ativos: %d", len(trailing_orders))
                for i, order in enumerate(trailing_orders, 1):
                    self.logger.info("Trailing Stop #%d:", i)
                    self.logger.info("  - ID: %s", order.get("id", "N/A"))
                    self.logger.info("  - Status: %s", order.get("status", "N/A"))
                    self.logger.info("  - Lado: %s", order.get("side", "N/A"))
                    self.logger.info("  - Quantidade: %s", order.get("amount", "N/A"))
                    self.logger.info(
                        "  - Trigger: %s", order.get("triggerPrice", "N/A")
                    )
                self.logger.info("Nova ordem BLOQUEADA para %s", symbol)
                self.logger.info("=====================================")
        except Exception as exc:
            self.logger.error("Erro ao registrar detalhes de prevenção: %s", str(exc))


class SwingTradingBot(TradingBotBase):
    """Bot de trading específico para estratégia Swing com Trailing Stop."""

    async def _execute_strategy(self, client: OKXClient, indicator: TechnicalIndicator, 
                               signal_checker: SwingSignalGenerator, order_manager: OKXOrder, strategy_type: str) -> None:
        """
        Implementação da lógica de trading específica para estratégia Swing com Trailing Stop.
        
        Args:
            client: Instance of OKXClient for exchange interactions.
            indicator: TechnicalIndicator instance for calculating indicators.
            signal_checker: SwingSignalGenerator instance for checking entry signals.
            order_manager: OKXOrder instance for managing order placement.
            strategy_type: Type of strategy for notifications.
        """
        print("\n🤖 Verificando Sinais de Trading:")
        print("=" * 50)
        
        # Obter tickers de uma vez para todos os símbolos para otimizar
        tickers = client.exchange.fetch_tickers(client.config.TRADING_SYMBOLS)

        for symbol in client.config.TRADING_SYMBOLS:
            try:
                has_buy_signal = signal_checker.check_entry_signal(symbol, timeframe=None)
                signal_checker.print_signal(symbol, timeframe=None, tickers=tickers)
                
                if order_manager.has_active_trailing_stop(symbol):
                    continue
                if has_buy_signal:
                    self.logger.info(f"\n🚨 SINAL DE COMPRA DETECTADO PARA {symbol}!")
                    if order_manager.validate_order_conditions(symbol):
                        self.logger.info(f"✅ Condições validadas para {symbol}")
                        order_result = await order_manager.place_buy_order_with_trailing_stop(
                            symbol=symbol, indicator=indicator
                        )
                        if order_result:
                            print(f"🎯 Swing Trail Order - ORDEM EXECUTADA COM SUCESSO!")
                            print(f"• Símbolo: {symbol}")
                            print(f"• Quantidade: {order_result['amount']}")
                            print(f"• Preço: ${order_result['entry_price']}")
                            print(
                                f"• ID da ordem: {order_result['limit_order'].get('id', 'N/A')}"
                            )

                            self.save_order_to_db(order_result["limit_order"])
                            self.created_orders.append(order_result["limit_order"])
                            await self.play_alert("success", volume=0.7)

                            message = f"""🎯 *ORDEM SWING TRAILING STOP EXECUTADA*
• *Bot*: {self.config.SCRIPT_NAME}
• *Símbolo*: {symbol}
• *Tipo*: Trailing Stop (Limit)
• *Quantidade*: {order_result['amount']}
• *Preço*: ${order_result['entry_price']}
"""
                            await self.send_telegram_notification(message)
                        else:
                            self.logger.error(f"❌ Falha ao executar ordem para {symbol}")
                            await self.play_alert("error", volume=0.5)
                    else:
                        self.logger.warning(f"⚠️ Condições não atendidas para {symbol}")
                else:
                    self.logger.info(f"Swing | {symbol} -> Wait")
                    print("")
            except Exception as exc:
                self.logger.error("Erro ao processar %s: %s", symbol, str(exc))
                print(f"❌ Erro ao processar {symbol}: {exc}")


class TradingBot(SwingTradingBot):
    """Classe para compatibilidade com chamadas internas que esperam TradingBot."""
    pass


async def main() -> None:
    """Função principal com auto-trading implementado para estratégia Swing com Trailing Stop."""
    logger = TradingLogger.get_logger(__name__)

    try:
        config = BotConfig(strategy="swing")
        config.SCRIPT_NAME = "999 Base Trail Swing"
        client = OKXClient(config)
        # Ensure TRADING_SYMBOLS from config are used in client
        client.config.TRADING_SYMBOLS = config.TRADING_SYMBOLS
        order_manager = OKXOrder(client)  # Inicializar order_manager específico para Trailing Stop
        bot = TradingBot(config)
        await send_startup_notification(client, bot)
        await bot.play_alert("start", volume=0.25)

        indicator = TechnicalIndicator(client)

        # Inicializar dados históricos
        for symbol in client.config.TRADING_SYMBOLS:
            indicator.fetch_historical_data(symbol, client.config.TIMEFRAME)

        signal_checker = SwingSignalGenerator(indicator, client)
        await bot.run_trading_loop(client, indicator, signal_checker, order_manager, "SWING")

    except KeyboardInterrupt:
        logger.info("Bot interrompido manualmente pelo usuário.")
        print("\n🛑 Bot interrompido manualmente.")
        if "bot" in locals():
            await bot.play_alert("exit", volume=0.9)
        print("A finalizar o bot. Até breve!")
        return

    except (ConfigurationError, ExchangeConnectionError) as exc:
        logger.error("Erro de configuração/conexão: %s", exc)
        print(f"❌ Erro: {exc}")
        print("Por favor, verifique as suas credenciais e configuração.")

    except Exception as exc:
        logger.error("Erro inesperado: %s", exc)
        logger.exception(exc)
        print(f"❌ Erro inesperado: {exc}")
        print("Verifique os logs para mais detalhes.")


if __name__ == "__main__":
    asyncio.run(main())
