"""
Backtester for Swing Trading Strategy with Trailing Stop
"""

import pandas as pd
import numpy as np
import talib
import matplotlib.pyplot as plt
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

# Import necessary modules from the trading bot
from core.config import BotConfig
from core.okx_client import OK<PERSON><PERSON>lient
from indicators.indicators import TechnicalIndicator
from signals.swing import SwingSignalGenerator


class SwingTrailBacktester:
    """Backtester for Swing Trading Strategy with Trailing Stop"""
    
    def __init__(self, symbol: str, timeframe: str = "1h", 
                 start_date: str = None, end_date: str = None,
                 initial_balance: float = 100000.0):
        """
        Initialize the backtester
        
        Args:
            symbol: Trading pair to backtest
            timeframe: Timeframe for the backtest
            start_date: Start date for backtest data (format: 'YYYY-MM-DD')
            end_date: End date for backtest data (format: 'YYYY-MM-DD')
            initial_balance: Initial balance for the backtest
        """
        self.symbol = symbol
        self.timeframe = timeframe
        self.start_date = start_date
        self.end_date = end_date
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        
        # Initialize configuration
        self.config = BotConfig(strategy="swing")
        self.client = OKXClient(self.config)
        self.indicator = TechnicalIndicator(self.client)
        self.signal_generator = SwingSignalGenerator(self.indicator, self.client)
        
        # Trading parameters
        self.position_size = self.config.POSITION_SIZE_BASE
        self.enable_dynamic_position = self.config.ENABLE_DYNAMIC_POSITION_SIZING
        self.enable_dynamic_trailing = self.config.ENABLE_DYNAMIC_TRAILING_RATIO
        
        # Results storage
        self.trades = []
        self.equity_curve = []
    
    def load_data(self) -> pd.DataFrame:
        """Load historical data for backtesting from CSV file"""
        # Construct the file path
        file_path = f"data/data_{self.symbol.replace('/', '_')}_{self.timeframe}.csv"
        
        try:
            # Load data from CSV
            df = pd.read_csv(file_path)
            
            # Check if timestamp column exists
            if 'timestamp' in df.columns:
                # Convert timestamp to datetime
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            else:
                # If no timestamp column, try to use the first column as index
                df = pd.read_csv(file_path, index_col=0)
                df.index = pd.to_datetime(df.index)
            
            # Ensure required columns exist
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"CSV file missing required columns: {missing_columns}")
            
            # Filter by date range if provided
            if self.start_date:
                df = df[df.index >= self.start_date]
            if self.end_date:
                df = df[df.index <= self.end_date]
                
            print(f"Loaded {len(df)} rows of historical data from {file_path}")
            
            # Calculate indicators
            self._calculate_indicators(df)
            
            return df
            
        except FileNotFoundError:
            raise FileNotFoundError(f"Historical data file not found: {file_path}. "
                                   f"Please ensure the file exists in the data directory.")
    
    def _calculate_indicators(self, df: pd.DataFrame) -> None:
        """Calculate indicators needed for the strategy"""
        # RSI
        df['rsi'] = talib.RSI(df['close'], timeperiod=self.config.RSI_PERIOD)
        
        # WMA
        df['wma'] = talib.WMA(df['close'], timeperiod=self.config.WMA_PERIOD)
        
        # ATR for trailing stop calculation
        df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
        
        # Swing high/low detection
        self._calculate_swing_points(df)
    
    def _calculate_swing_points(self, df: pd.DataFrame) -> None:
        """Calculate swing high and low points"""
        swing_bars = self.config.SWING_BARS
        swing_bars2 = self.config.SWING_BARS2
        
        # Calculate resistance and support
        df['res'] = df['high'].rolling(window=swing_bars).max()
        df['sup'] = df['low'].rolling(window=swing_bars).min()
        
        # Calculate swing signals
        df['avd'] = 0
        
        # Logic from SwingSignalGenerator
        for i in range(swing_bars2, len(df)):
            if df['close'].iloc[i] > df['res'].iloc[i-swing_bars2]:
                df['avd'].iloc[i] = 1
            elif df['close'].iloc[i] < df['sup'].iloc[i-swing_bars2]:
                df['avd'].iloc[i] = -1
    
    def _calculate_dynamic_position_size(self, df: pd.DataFrame, idx: int) -> float:
        """Calculate dynamic position size based on volatility"""
        if not self.enable_dynamic_position:
            return self.config.POSITION_SIZE_BASE
        
        atr = df['atr'].iloc[idx]
        if pd.isna(atr):
            return self.config.POSITION_SIZE_BASE
        
        # Calculate average ATR for comparison
        lookback = self.config.ATR_LOOKBACK_PERIODS
        if idx >= lookback + 14:
            atr_values = df['atr'].iloc[idx-lookback:idx].values
            atr_mean = np.mean(atr_values)
            volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0
            
            if volatility_ratio > self.config.VOLATILITY_THRESHOLD_HIGH:
                return self.config.POSITION_SIZE_MIN
            elif volatility_ratio < self.config.VOLATILITY_THRESHOLD_LOW:
                return self.config.POSITION_SIZE_MAX
        
        return self.config.POSITION_SIZE_BASE
    
    def _calculate_callback_ratio(self, df: pd.DataFrame, idx: int, entry_price: float) -> float:
        """Calculate callback ratio for trailing stop"""
        atr = df['atr'].iloc[idx]
        if pd.isna(atr):
            return 0.03  # Default
        
        # Base calculation from the original code
        callback_ratio_base = (1.75 * atr) / entry_price
        
        if not self.enable_dynamic_trailing:
            return max(0.015, min(0.08, callback_ratio_base))
        
        # Dynamic adjustment based on volatility
        lookback = self.config.ATR_LOOKBACK_PERIODS
        if idx >= lookback + 14:
            atr_values = df['atr'].iloc[idx-lookback:idx].values
            atr_mean = np.mean(atr_values)
            volatility_ratio = atr / atr_mean if atr_mean > 0 else 1.0
            
            if volatility_ratio > self.config.VOLATILITY_THRESHOLD_HIGH:
                return max(self.config.CALLBACK_RATIO_MAX, callback_ratio_base)
            elif volatility_ratio < self.config.VOLATILITY_THRESHOLD_LOW:
                return min(self.config.CALLBACK_RATIO_MIN, callback_ratio_base)
            else:
                return max(
                    self.config.CALLBACK_RATIO_BASE,
                    min(callback_ratio_base, self.config.CALLBACK_RATIO_MAX)
                )
        
        return max(0.015, min(0.08, callback_ratio_base))
    
    def run_backtest(self) -> Dict[str, Any]:
        """Run the backtest and return results"""
        df = self.load_data()
        
        # Initialize tracking variables
        position = False
        entry_price = 0.0
        entry_idx = 0
        position_size = 0.0
        callback_ratio = 0.0
        highest_price = 0.0
        trailing_stop = 0.0
        
        # For each candle
        for idx in range(len(df)):
            current_price = df['close'].iloc[idx]
            current_date = df.index[idx]
            
            # Track equity
            self.equity_curve.append({
                'date': current_date,
                'equity': self.current_balance,
                'price': current_price,
                'in_position': position
            })
            
            # Check trailing stop if in position
            if position:
                # Update highest price seen
                if current_price > highest_price:
                    highest_price = current_price
                    # Recalculate trailing stop
                    trailing_stop = highest_price * (1 - callback_ratio)
                
                # Check if trailing stop is hit
                if current_price <= trailing_stop:
                    # Close position
                    exit_price = current_price
                    profit_pct = (exit_price / entry_price - 1) * 100
                    profit_amount = self.current_balance * position_size * profit_pct / 100
                    self.current_balance += profit_amount
                    
                    # Record trade
                    self.trades.append({
                        'entry_date': df.index[entry_idx],
                        'exit_date': current_date,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'profit_pct': profit_pct,
                        'profit_amount': profit_amount,
                        'balance_after': self.current_balance,
                        'position_size': position_size,
                        'callback_ratio': callback_ratio,
                        'highest_price': highest_price,
                        'trailing_stop': trailing_stop
                    })
                    
                    position = False
                    continue
            
            # Check for entry signal if not in position
            if not position and idx > self.config.SWING_BARS2:
                # Check buy signal (avd == 1)
                if df['avd'].iloc[idx] == 1:
                    # Enter position
                    entry_price = current_price
                    entry_idx = idx
                    position = True
                    
                    # Calculate position size and callback ratio
                    position_size = self._calculate_dynamic_position_size(df, idx)
                    callback_ratio = self._calculate_callback_ratio(df, idx, entry_price)
                    
                    # Initialize trailing variables
                    highest_price = entry_price
                    trailing_stop = highest_price * (1 - callback_ratio)
        
        # Close any open position at the end
        if position:
            exit_price = df['close'].iloc[-1]
            profit_pct = (exit_price / entry_price - 1) * 100
            profit_amount = self.current_balance * position_size * profit_pct / 100
            self.current_balance += profit_amount
            
            self.trades.append({
                'entry_date': df.index[entry_idx],
                'exit_date': df.index[-1],
                'entry_price': entry_price,
                'exit_price': exit_price,
                'profit_pct': profit_pct,
                'profit_amount': profit_amount,
                'balance_after': self.current_balance,
                'position_size': position_size,
                'callback_ratio': callback_ratio,
                'highest_price': highest_price,
                'trailing_stop': trailing_stop
            })
        
        # Calculate performance metrics
        return self._calculate_performance()
    
    def _calculate_performance(self) -> Dict[str, Any]:
        """Calculate performance metrics from backtest results"""
        if not self.trades:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'total_return': 0,
                'max_drawdown': 0,
                'sharpe_ratio': 0
            }
        
        # Convert trades to DataFrame for analysis
        trades_df = pd.DataFrame(self.trades)
        equity_df = pd.DataFrame(self.equity_curve)
        
        # Basic metrics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['profit_pct'] > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Profit metrics
        total_profit = trades_df[trades_df['profit_pct'] > 0]['profit_amount'].sum()
        total_loss = abs(trades_df[trades_df['profit_pct'] <= 0]['profit_amount'].sum())
        profit_factor = total_profit / total_loss if total_loss != 0 else float('inf')
        
        # Return metrics
        total_return_pct = (self.current_balance / self.initial_balance - 1) * 100
        
        # Drawdown calculation
        equity_df['peak'] = equity_df['equity'].cummax()
        equity_df['drawdown'] = (equity_df['equity'] / equity_df['peak'] - 1) * 100
        max_drawdown = abs(equity_df['drawdown'].min())
        
        # Risk-adjusted return (simplified Sharpe)
        if len(equity_df) > 1:
            equity_returns = equity_df['equity'].pct_change().dropna()
            sharpe_ratio = (equity_returns.mean() / equity_returns.std()) * np.sqrt(252) if equity_returns.std() != 0 else 0
        else:
            sharpe_ratio = 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_return_pct': total_return_pct,
            'final_balance': self.current_balance,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'trades': self.trades,
            'equity_curve': self.equity_curve
        }
    
    def plot_results(self) -> None:
        """Plot backtest results"""
        if not self.trades or not self.equity_curve:
            print("No trades to plot")
            return
        
        equity_df = pd.DataFrame(self.equity_curve)
        trades_df = pd.DataFrame(self.trades)
        
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [2, 1]})
        
        # Plot price chart with buy/sell points
        ax1.plot(equity_df['date'], equity_df['price'], label='Price', color='blue', alpha=0.5)
        
        # Plot buy points
        for _, trade in trades_df.iterrows():
            ax1.scatter(trade['entry_date'], trade['entry_price'], color='green', marker='^', s=100)
            ax1.scatter(trade['exit_date'], trade['exit_price'], color='red', marker='v', s=100)
        
        ax1.set_title(f'Backtest Results for {self.symbol} ({self.timeframe})')
        ax1.set_ylabel('Price')
        ax1.legend()
        ax1.grid(True)
        
        # Plot equity curve
        ax2.plot(equity_df['date'], equity_df['equity'], label='Equity', color='green')
        ax2.set_xlabel('Date')
        ax2.set_ylabel('Equity')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        
        # Save plot
        os.makedirs('backtest_results', exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        plt.savefig(f'backtest_results/backtest_{self.symbol.replace("/", "_")}_{timestamp}.png')
        plt.show()
    
    def save_results(self) -> None:
        """Save backtest results to CSV files"""
        if not self.trades or not self.equity_curve:
            print("No results to save")
            return
        
        os.makedirs('backtest_results', exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save trades
        trades_df = pd.DataFrame(self.trades)
        trades_df.to_csv(f'backtest_results/trades_{self.symbol.replace("/", "_")}_{timestamp}.csv', index=False)
        
        # Save equity curve
        equity_df = pd.DataFrame(self.equity_curve)
        equity_df.to_csv(f'backtest_results/equity_{self.symbol.replace("/", "_")}_{timestamp}.csv', index=False)
        
        print(f"Results saved to backtest_results/ directory")


def main():
    """Main function to run the backtest"""
    # Example usage
    symbol = "BTC/USDC"  # Replace with your desired symbol
    timeframe = "1h"    # Replace with your desired timeframe
    initial_balance = 1000.0  # Starting balance
    
    # Optional date range
    start_date = "2023-01-01"
    end_date = "2023-12-31"
    
    print(f"Running backtest for {symbol} on {timeframe} timeframe...")
    
    backtester = SwingTrailBacktester(
        symbol=symbol,
        timeframe=timeframe,
        start_date=start_date,
        end_date=end_date,
        initial_balance=initial_balance
    )
    
    results = backtester.run_backtest()
    
    # Print summary
    print("\n===== BACKTEST RESULTS =====")
    print(f"Symbol: {symbol}")
    print(f"Timeframe: {timeframe}")
    print(f"Period: {start_date} to {end_date}")
    print(f"Initial Balance: ${initial_balance:.2f}")
    print(f"Final Balance: ${results['final_balance']:.2f}")
    print(f"Total Return: {results['total_return_pct']:.2f}%")
    print(f"Total Trades: {results['total_trades']}")
    print(f"Win Rate: {results['win_rate']*100:.2f}%")
    print(f"Profit Factor: {results['profit_factor']:.2f}")
    print(f"Maximum Drawdown: {results['max_drawdown']:.2f}%")
    print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    
    # Plot and save results
    backtester.plot_results()
    backtester.save_results()


if __name__ == "__main__":
    main()
