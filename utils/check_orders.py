from typing import List, Dict

def check_regular_orders(client, symbol: str) -> List[Dict]:
    try:
        # Verificar se client tem atributo exchange, caso contr<PERSON>rio, assumir que client é a exchange
        exchange = getattr(client, 'exchange', client)
        open_orders = exchange.fetch_open_orders(symbol)
        regular_orders = [
            order
            for order in open_orders
            if order.get("type") == "limit" or order.get("type") == "market"
        ]
        client.logger.debug(
            "Ordens regulares verificadas para %s: %d encontradas",
            symbol,
            len(regular_orders),
        )
        return regular_orders
    except Exception as exc:
        client.logger.error("Erro ao verificar ordens para %s: %s", symbol, str(exc))
        raise Exception(f"Falha ao verificar ordens para {symbol}") from exc

def display_regular_orders(client) -> None:
    from tabulate import tabulate
    print("\n📉 Ordens Regulares Abertas:")
    print("=" * 118)
    try:
        found_orders = False
        for symbol in client.config.TRADING_SYMBOLS:
            regular_orders = check_regular_orders(client, symbol)
            if regular_orders:
                found_orders = True
                print(f" 📌 {symbol}:")
                headers = ["Símbolo", "ID", "Tipo", "Lado", "Quantidade", "Preço"]
                table_data = [
                    [
                        symbol,
                        order.get("id", "N/A"),
                        order.get("type", "N/A"),
                        order.get("side", "N/A"),
                        order.get("amount", "N/A"),
                        order.get("price", "N/A"),
                    ]
                    for order in regular_orders[:5]
                ]
                print(tabulate(table_data, headers, tablefmt="fancy_grid"))
                if len(regular_orders) > 5:
                    print(f" ... e mais {len(regular_orders) - 5} ordens.")
        if not found_orders:
            print(" Nenhuma ordem regular aberta encontrada.")
    except Exception as exc:
        client.logger.error(
            "Erro ao exibir ordens regulares: name 'tabulate' is not defined",
            exc_info=exc,
        )
        print(f" ❌ Erro ao verificar ordens regulares: {exc}")

def check_oco_orders(client, symbol: str) -> List[Dict]:
    try:
        # Verificar se client tem atributo exchange, caso contrário, assumir que client é a exchange
        exchange = getattr(client, 'exchange', client)
        oco_orders = exchange.fetch_open_orders(
            symbol, params={"trigger": True, "ordType": "oco"}
        )
        client.logger.debug(
            "Ordens OCO verificadas para %s: %d encontradas",
            symbol,
            len(oco_orders),
        )
        return oco_orders
    except Exception as exc:
        client.logger.error(
            "Erro ao verificar ordens OCO para %s: %s", symbol, str(exc)
        )
        raise Exception(
            f"Falha ao verificar ordens OCO para {symbol}"
        ) from exc

def display_oco_orders(client) -> None:
    from tabulate import tabulate
    print("\n📉 Ordens OCO Abertas:")
    print("=" * 118)
    try:
        found_orders = False
        for symbol in client.config.TRADING_SYMBOLS:
            oco_orders = check_oco_orders(client, symbol)
            if oco_orders:
                found_orders = True
                print(f" 📌 {symbol}:")
                tabular_data = []
                for order in oco_orders[:5]:
                    order_id = order.get("id", "N/A")
                    side = order.get("side", "N/A")
                    amount = order.get("amount", "N/A")
                    price = order.get("price", "N/A")
                    trigger_price = order.get("triggerPrice", "N/A")
                    status = order.get("status", "N/A")
                    stop_loss = (
                        order.get("stopLossPrice")
                        or order.get("stop_loss_price")
                        or order.get("stopPrice")
                        or order.get("stop_price")
                        or (order.get("triggerPrice") if side == "sell" else None)
                        or "N/A"
                    )
                    take_profit = (
                        order.get("takeProfitPrice")
                        or order.get("take_profit_price")
                        or (order.get("triggerPrice") if side == "buy" else None)
                        or "N/A"
                    )
                    tabular_data.append(
                        [
                            symbol,
                            order_id,
                            side,
                            amount,
                            price,
                            trigger_price,
                            stop_loss,
                            take_profit,
                            status,
                        ]
                    )
                print(
                    tabulate(
                        tabular_data,
                        headers=[
                            "Símbolo",
                            "ID",
                            "Lado",
                            "Quantidade",
                            "Preço",
                            "Trigger",
                            "Stop Loss",
                            "Take Profit",
                            "Status",
                        ],
                        tablefmt="fancy_grid",
                    )
                )
                if len(oco_orders) > 5:
                    print(f" ... e mais {len(oco_orders) - 5} ordens OCO.")
        if not found_orders:
            print(" Nenhuma ordem OCO aberta encontrada.")
    except Exception as exc:
        client.logger.error("Erro ao exibir ordens OCO: %s", str(exc))
        print(f" ❌ Erro ao verificar ordens OCO: {exc}")

def check_trailing_stop_orders(client, symbol: str) -> List[Dict]:
    try:
        # Verificar se client tem atributo exchange, caso contrário, assumir que client é a exchange
        exchange = getattr(client, 'exchange', client)
        # Obter ordens algorítmicas pendentes específicas para trailing stop
        inst_id = exchange.markets[symbol]["id"]
        algo_orders = exchange.private_get_trade_orders_algo_pending({
            "instId": inst_id,
            "ordType": "move_order_stop"
        })
        
        trailing_orders = []
        if algo_orders and "data" in algo_orders:
            for order in algo_orders["data"]:
                # Mapear campos relevantes para o formato esperado
                formatted_order = {
                    "id": order.get("algoId", "N/A"),
                    "symbol": symbol,
                    "side": order.get("side", "N/A").lower(),
                    "amount": float(order.get("sz", "0")),
                    "status": "open" if order.get("state") == "live" else order.get("state", "N/A"),
                    "type": "move_order_stop",
                    "triggerPrice": float(order.get("activePx", "0")) if order.get("activePx") else "N/A",
                    "trailingPercent": float(order.get("callbackRatio", "0")) * 100 if order.get("callbackRatio") else "N/A",  # Converter para porcentagem
                    "trailingAmount": "N/A"  # Será calculado na exibição, se possível
                }
                trailing_orders.append(formatted_order)
        
        client.logger.debug(
            "Ordens Trailing Stop verificadas para %s: %d encontradas",
            symbol,
            len(trailing_orders),
        )
        return trailing_orders
    except Exception as exc:
        client.logger.error(
            "Erro ao verificar ordens Trailing Stop para %s: %s", symbol, str(exc)
        )
        return []

def display_trailing_stop_orders(client) -> None:
    from tabulate import tabulate
    print("\n📉 Ordens Trailing Stop Abertas:")
    print("=" * 118)
    try:
        found_orders = False
        for symbol in client.config.TRADING_SYMBOLS:
            trailing_orders = check_trailing_stop_orders(client, symbol)
            if trailing_orders:
                found_orders = True
                print(f" 📌 {symbol}:")
                headers = [
                    "Símbolo",
                    "ID",
                    "Lado",
                    "Quantidade",
                    "Trigger",
                    "Trailing %",
                    "Trailing Amount",
                    "Estado",
                ]
                table = []
                for order in trailing_orders[:5]:
                    order_id = order.get("id", "N/A")
                    side = order.get("side", "N/A")
                    amount = order.get("amount", "N/A")
                    if isinstance(amount, (int, float)):
                        amount = client.format_amount_with_precision(symbol, amount)
                    trigger_price = order.get("triggerPrice", "N/A")
                    trailing_percent = order.get("trailingPercent", "N/A")
                    # Calcular Trailing Amount se possível
                    if trailing_percent != "N/A" and trigger_price != "N/A":
                        trailing_amount = (trailing_percent / 100) * trigger_price
                        trailing_amount = round(trailing_amount, 2)
                    else:
                        trailing_amount = "N/A"
                    status = order.get("status", "N/A")
                    table.append(
                        [
                            symbol,
                            order_id,
                            side,
                            amount,
                            trigger_price,
                            f"{trailing_percent:.2f}%" if isinstance(trailing_percent, (int, float)) else trailing_percent,
                            trailing_amount,
                            status,
                        ]
                    )
                print(tabulate(table, headers, tablefmt="fancy_grid"))
                if len(trailing_orders) > 5:
                    print(
                        f" ... e mais {len(trailing_orders) - 5} ordens Trailing Stop."
                    )
        if not found_orders:
            print(" Nenhuma ordem Trailing Stop aberta encontrada.")
    except Exception as exc:
        client.logger.error("Erro ao exibir ordens Trailing Stop: %s", str(exc))
        print(f" ❌ Erro ao verificar ordens Trailing Stop: {exc}")
